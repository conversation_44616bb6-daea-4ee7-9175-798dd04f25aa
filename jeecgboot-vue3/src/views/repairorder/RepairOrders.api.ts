import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/repairorder/repairOrders/list',
  save='/repairorder/repairOrders/add',
  edit='/repairorder/repairOrders/edit',
  deleteOne = '/repairorder/repairOrders/delete',
  deleteBatch = '/repairorder/repairOrders/deleteBatch',
  importExcel = '/repairorder/repairOrders/importExcel',
  exportXls = '/repairorder/repairOrders/exportXls',
  queryById = '/repairorder/repairOrders/queryById',
  audit = '/repairorder/repairOrders/audit',
  getUserPermission = '/repairorder/repairOrders/getUserPermission',
  complete = '/repairorder/repairOrders/complete',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

/**
 * 根据ID查询工单详情（包含操作日志）
 * @param params
 */
export const queryById = (params) => {
  return defHttp.get({url: Api.queryById, params});
}

/**
 * 审核工单
 * @param params
 */
export const auditOrder = (params) => {
  let url = `${Api.audit}?id=${params.id}&auditStatus=${params.auditStatus}&remark=${encodeURIComponent(params.remark || '')}`;
  // 如果是审核通过，需要传递负责人ID
  if (params.auditStatus === 'approve' && params.principalId) {
    url += `&principalId=${encodeURIComponent(params.principalId)}`;
  }
  return defHttp.post({url});
}

/**
 * 获取用户权限
 * @param params
 */
export const getUserPermission = (params) => {
  return defHttp.get({url: Api.getUserPermission, params});
}

/**
 * 处理完成工单
 * @param params
 */
export const completeOrder = (params) => {
  const url = `${Api.complete}?id=${params.id}&handleImages=${encodeURIComponent(params.handleImages || '')}&handleAttachment=${encodeURIComponent(params.handleAttachment || '')}&remark=${encodeURIComponent(params.remark || '')}`;
  return defHttp.post({url});
}
