<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable">
     <!--插槽:table标题-->
      <template #tableTitle>
          <a-button type="primary" v-auth="'repairorder:repair_orders:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
          <a-button  type="primary" v-auth="'repairorder:repair_orders:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
          <j-upload-button type="primary" v-auth="'repairorder:repair_orders:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>


        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <RepairOrdersModal @register="registerModal" @success="handleSuccess"></RepairOrdersModal>
    <AuditModal @register="registerAuditModal" @success="handleSuccess"></AuditModal>
    <CompleteModal @register="registerCompleteModal" @success="handleSuccess"></CompleteModal>
  </div>
</template>

<script lang="ts" name="repairorder-repairOrders" setup>
  import {ref, reactive, computed, unref} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import RepairOrdersModal from './components/RepairOrdersModal.vue'
  import AuditModal from './components/AuditModal.vue'
  import CompleteModal from './components/CompleteModal.vue'
  import {columns, searchFormSchema, superQuerySchema} from './RepairOrders.data';
  import {list, getImportUrl,getExportUrl, auditOrder, getUserPermission} from './RepairOrders.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getDateByPicker } from '/@/utils';
  //日期个性化选择
  const fieldPickers = reactive({
  });
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  const { createMessage } = useMessage();
  //注册model
  const [registerModal, {openModal}] = useModal();
  const [registerAuditModal, {openModal: openAuditModal}] = useModal();
  const [registerCompleteModal, {openModal: openCompleteModal}] = useModal();
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
      tableProps:{
           title: '维修工单管理',
           api: list,
           columns,
           canResize:true,
           formConfig: {
              //labelWidth: 120,
              schemas: searchFormSchema,
              autoSubmitOnEnter:true,
              showAdvancedButton:true,
              fieldMapToNumber: [
              ],
              fieldMapToTime: [
              ],
            },
           actionColumn: {
               width: 200,
               fixed:'right'
            },
            beforeFetch: (params) => {
              if (params && fieldPickers) {
                for (let key in fieldPickers) {
                  if (params[key]) {
                    params[key] = getDateByPicker(params[key], fieldPickers[key]);
                  }
                }
              }
              return Object.assign(params, queryParam);
            },
      },
      getTableAction,
      getDropDownAction,
       exportConfig: {
            name:"repairorder",
            url: getExportUrl,
            params: queryParam,
          },
          importConfig: {
            url: getImportUrl,
            success: handleSuccess
          },
  })

  const [registerTable, {reload}] = tableContext

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
   /**
    * 新增事件
    */
  function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
     });
  }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }

   /**
    * 成功回调
    */
  function handleSuccess() {
      reload();
   }

   /**
    * 审核工单
    */
   function handleAudit(record, auditStatus) {
     // 验证当前用户是否为审核人
     const currentUser = userStore.getUserInfo;
     const currentUserId = currentUser?.username;

     if (currentUserId !== record.auditorId) {
       createMessage.error('只有审核人才能进行审核操作！');
       return;
     }

     // 只有审核中或重新提交状态的工单才能进行审核
     if (record.currentStatus !== '1' && record.currentStatus !== '3') {
       createMessage.error('只有审核中或重新提交状态的工单才能进行审核！');
       return;
     }

     openAuditModal(true, {
       record: record,
       auditStatus: auditStatus
     });
   }

   /**
    * 处理完成工单
    */
   function handleComplete(record) {
     // 验证当前用户是否为负责人
     const currentUser = userStore.getUserInfo;
     const currentUserId = currentUser?.username;

     if (currentUserId !== record.principalId) {
       createMessage.error('只有负责人才能进行处理完成操作！');
       return;
     }

     if (record.currentStatus !== '4') {
       createMessage.error('只有处理中状态的工单才能进行处理完成！');
       return;
     }

     openCompleteModal(true, {
       record: record,
       isUpdate: true
     });
   }
   /**
      * 操作栏
      */
  function getTableAction(record){
       const actions = [
         {
           label: '详情',
           onClick: handleDetail.bind(null, record),
           color: 'primary'
         }
       ];

       // 获取当前用户信息
       const currentUser = userStore.getUserInfo;
       const currentUserId = currentUser?.username;

       // 根据工单状态和当前用户角色显示不同操作
       // 驳回状态的工单，发起人可以编辑
       if (record.currentStatus === '2' && currentUserId === record.reportId) {
         actions.push({
           label: '编辑',
           onClick: handleEdit.bind(null, record),
           auth: 'repairorder:repair_orders:edit'
         });
       }

       // 审核中状态或重新提交状态的工单，只有审核人可以审核
       if ((record.currentStatus === '1' || record.currentStatus === '3') && currentUserId === record.auditorId) {
         actions.push({
           label: '通过',
           onClick: () => handleAudit(record, 'approve'),
           color: 'success'
         });
         actions.push({
           label: '驳回',
           onClick: () => handleAudit(record, 'reject'),
           color: 'error'
         });
       }

       // 处理中状态的工单，只有负责人可以处理完成
       if (record.currentStatus === '4' && currentUserId === record.principalId) {
         actions.push({
           label: '处理完成',
           onClick: () => handleComplete(record),
           color: 'success'
         });
       }

       return actions;
   }
     /**
        * 下拉操作栏
        */
  function getDropDownAction(record){
       // 工单不可以删除，返回空数组
       return [];
   }




</script>

<style lang="less" scoped>
  :deep(.ant-picker),:deep(.ant-input-number){
    width: 100%;
  }
</style>
