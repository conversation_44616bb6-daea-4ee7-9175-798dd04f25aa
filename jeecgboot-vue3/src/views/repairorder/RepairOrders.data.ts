import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { h } from 'vue';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '工单编号',
    align:"center",
    dataIndex: 'id',
    width: 180,
    ellipsis: true,
   },
   {
    title: '故障标题',
    align:"left",
    dataIndex: 'faultTitle',
    width: 200,
    ellipsis: true,
   },
   {
    title: '关联设备',
    align:"center",
    dataIndex: 'equipmentId',
    width: 120,
   },
   {
    title: '发起人',
    align:"center",
    dataIndex: 'reportId_dictText',
    width: 100,
   },
   {
    title: '审核人',
    align:"center",
    dataIndex: 'auditorId_dictText',
    width: 100,
   },
   {
    title: '负责人',
    align:"center",
    dataIndex: 'principalId_dictText',
    width: 100,
    customRender: ({ text, record }) => {
      // 只有审核通过后（状态为4、5、6）才显示负责人
      const status = record.currentStatus;
      if (['4', '5', '6'].includes(status)) {
        return text || '未分配';
      }
      return '-';
    }
   },
   {
    title: '当前状态',
    align:"center",
    dataIndex: 'currentStatus',
    width: 100,
    customRender: ({ text }) => {
      const statusMap = {
        '1': { text: '审核中', color: '#1890ff' },
        '2': { text: '已驳回', color: '#ff4d4f' },
        '3': { text: '重新提交', color: '#faad14' },
        '4': { text: '处理中', color: '#52c41a' },
        '5': { text: '已完成', color: '#52c41a' },
        '6': { text: '已关闭', color: '#8c8c8c' }
      };
      const status = statusMap[text] || { text: text, color: '#8c8c8c' };
      return h('span', { style: { color: status.color, fontWeight: 'bold' } }, status.text);
    }
   },
   {
    title: '创建时间',
    align:"center",
    dataIndex: 'createTime',
    width: 150,
    sorter: true,
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '故障标题',
    field: 'faultTitle',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '当前状态',
    field: 'currentStatus',
    component: 'Select',
    componentProps: {
      options: [
        { label: '审核中', value: '1' },
        { label: '已驳回', value: '2' },
        { label: '重新提交', value: '3' },
        { label: '处理中', value: '4' },
        { label: '已完成', value: '5' },
        { label: '已关闭', value: '6' },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '发起人',
    field: 'reportId',
    component: 'JSelectUser',
    colProps: { span: 6 },
  },
  {
    label: '关联设备',
    field: 'equipmentId',
    component: 'Input',
    colProps: { span: 6 },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '关联设备',
    field: 'equipmentId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入关联设备!'},
          ];
     },
  },
  {
    label: '发起人',
    field: 'reportId',
    component: 'JSelectUser',
    componentProps:{
      placeholder: '请选择发起人'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择发起人!'},
          ];
     },
  },
  {
    label: '审核人',
    field: 'auditorId',
    component: 'JSelectUser',
    componentProps:{
      placeholder: '请选择审核人'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择审核人!'},
          ];
     },
  },
  {
    label: '故障标题',
    field: 'faultTitle',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入故障标题!'},
          ];
     },
  },
  {
    label: '故障描述',
    field: 'faultDescription',
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
      placeholder: '请详细描述故障情况'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入故障描述!'},
          ];
     },
  },
  {
    label: '故障图片',
    field: 'attachment',
    component: 'JImageUpload',
    componentProps:{
      fileMax: 5,
      text: '上传故障图片'
    },
  },
  {
    label: '故障附件',
    field: 'faultAttachment',
    component: 'JUpload',
    componentProps:{
      fileMax: 3,
      text: '上传故障附件'
    },
  },
];

// 创建工单表单配置（发起人使用，不包含负责人字段）
export const createFormSchema: FormSchema[] = [
  {
    label: '关联设备',
    field: 'equipmentId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入关联设备!'},
          ];
     },
  },
  {
    label: '发起人',
    field: 'reportId',
    component: 'JSelectUser',
    componentProps:{
      placeholder: '请选择发起人'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择发起人!'},
          ];
     },
  },
  {
    label: '审核人',
    field: 'auditorId',
    component: 'JSelectUser',
    componentProps:{
      placeholder: '请选择审核人'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择审核人!'},
          ];
     },
  },
  {
    label: '故障标题',
    field: 'faultTitle',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入故障标题!'},
          ];
     },
  },
  {
    label: '故障描述',
    field: 'faultDescription',
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
      placeholder: '请详细描述故障情况'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入故障描述!'},
          ];
     },
  },
  {
    label: '故障图片',
    field: 'attachment',
    component: 'JImageUpload',
    componentProps:{
      fileMax: 5,
      text: '上传故障图片'
    },
  },
  {
    label: '故障附件',
    field: 'faultAttachment',
    component: 'JUpload',
    componentProps:{
      fileMax: 3,
      text: '上传故障附件'
    },
  },
];

// 管理员表单配置（包含所有字段，用于管理员查看和编辑）
export const adminFormSchema: FormSchema[] = [
  {
    label: '关联设备',
    field: 'equipmentId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入关联设备!'},
          ];
     },
  },
  {
    label: '发起人',
    field: 'reportId',
    component: 'JSelectUser',
    componentProps:{
      placeholder: '请选择发起人'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择发起人!'},
          ];
     },
  },
  {
    label: '审核人',
    field: 'auditorId',
    component: 'JSelectUser',
    componentProps:{
      placeholder: '请选择审核人'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请选择审核人!'},
          ];
     },
  },
  {
    label: '负责人',
    field: 'principalId',
    component: 'JSelectUser',
    componentProps:{
      placeholder: '请选择负责人'
    },
  },
  {
    label: '故障标题',
    field: 'faultTitle',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入故障标题!'},
          ];
     },
  },
  {
    label: '故障描述',
    field: 'faultDescription',
    component: 'InputTextArea',
    componentProps: {
      rows: 4,
      placeholder: '请详细描述故障情况'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入故障描述!'},
          ];
     },
  },
  {
    label: '故障图片',
    field: 'attachment',
    component: 'JImageUpload',
    componentProps:{
      fileMax: 5,
      text: '上传故障图片'
    },
  },
  {
    label: '故障附件',
    field: 'faultAttachment',
    component: 'JUpload',
    componentProps:{
      fileMax: 3,
      text: '上传故障附件'
    },
  },
];

/**
 * 流程表单字段 - 根据工单状态和模式返回不同的表单配置
 * @param formData
 */
export function getBpmFormSchema(_formData): FormSchema[]{
  // 如果是创建模式，使用createFormSchema（不包含负责人字段）
  const isCreateMode = !_formData || !_formData.dataId;
  if (isCreateMode) {
    return createFormSchema;
  }

  // 如果是编辑/查看模式，根据工单状态决定是否显示负责人字段
  const currentStatus = _formData.currentStatus;

  // 审核通过后（状态为4、5、6）才显示负责人字段
  if (['4', '5', '6'].includes(currentStatus)) {
    return adminFormSchema;
  } else {
    // 审核前不显示负责人字段
    return createFormSchema;
  }
}

/**
 * 根据工单状态获取详情表单配置
 * @param orderData 工单数据
 */
export function getDetailFormSchema(orderData): FormSchema[] {
  const baseSchema = [
    {
      label: '工单编号',
      field: 'id',
      component: 'Input',
      componentProps: { disabled: true },
    },
    {
      label: '关联设备',
      field: 'equipmentId',
      component: 'Input',
      componentProps: { disabled: true },
    },
    {
      label: '发起人',
      field: 'reportId_dictText',
      component: 'Input',
      componentProps: { disabled: true },
    },
    {
      label: '审核人',
      field: 'auditorId_dictText',
      component: 'Input',
      componentProps: { disabled: true },
    },
  ];

  // 只有审核通过后才显示负责人字段
  if (['4', '5', '6'].includes(orderData?.currentStatus)) {
    baseSchema.push({
      label: '负责人',
      field: 'principalId_dictText',
      component: 'Input',
      componentProps: { disabled: true },
    });
  }

  baseSchema.push(
    {
      label: '故障标题',
      field: 'faultTitle',
      component: 'Input',
      componentProps: { disabled: true },
    },
    {
      label: '故障描述',
      field: 'faultDescription',
      component: 'InputTextArea',
      componentProps: { disabled: true, rows: 4 },
    },
    {
      label: '当前状态',
      field: 'currentStatus',
      component: 'Select',
      componentProps: {
        disabled: true,
        options: [
          { label: '审核中', value: '1' },
          { label: '已驳回', value: '2' },
          { label: '重新提交', value: '3' },
          { label: '处理中', value: '4' },
          { label: '已完成', value: '5' },
          { label: '已关闭', value: '6' },
        ]
      },
    }
  );

  return baseSchema;
}

// 操作日志表格列配置
export const auditLogColumns: BasicColumn[] = [
  {
    title: '操作时间',
    align: "center",
    dataIndex: 'createTime',
    width: 160,
    sorter: true,
  },
  {
    title: '操作人角色',
    align: "center",
    dataIndex: 'operatorType',
    width: 100,
    customRender: ({ text }) => {
      const roleMap = {
        '0': { text: '发起人', color: '#1890ff' },
        '1': { text: '审核人', color: '#52c41a' },
        '2': { text: '负责人', color: '#faad14' }
      };
      const role = roleMap[text] || { text: text, color: '#8c8c8c' };
      return h('span', { style: { color: role.color, fontWeight: 'bold' } }, role.text);
    }
  },
  {
    title: '操作类型',
    align: "center",
    dataIndex: 'actionType',
    width: 120,
    customRender: ({ text }) => {
      const actionMap = {
        '1': { text: '提交工单', color: '#1890ff' },
        '2': { text: '审核通过', color: '#52c41a' },
        '3': { text: '审核驳回', color: '#ff4d4f' },
        '4': { text: '重新提交', color: '#faad14' },
        '5': { text: '开始处理', color: '#722ed1' },
        '6': { text: '处理完成', color: '#52c41a' },
        '7': { text: '关闭工单', color: '#8c8c8c' },
        '8': { text: '其他操作', color: '#8c8c8c' }
      };
      const action = actionMap[text] || { text: text, color: '#8c8c8c' };
      return h('span', { style: { color: action.color, fontWeight: 'bold' } }, action.text);
    }
  },
  {
    title: '操作备注',
    align: "left",
    dataIndex: 'remark',
    ellipsis: true,
    width: 200,
  },
];

// 高级查询配置
export const superQuerySchema = {
  repairorder: {
    title: '维修工单',
    columns: [
      {
        title: '工单编号',
        dataIndex: 'id',
        key: 'id',
      },
      {
        title: '故障标题',
        dataIndex: 'faultTitle',
        key: 'faultTitle',
      },
      {
        title: '关联设备',
        dataIndex: 'equipmentId',
        key: 'equipmentId',
      },
      {
        title: '发起人',
        dataIndex: 'reportId',
        key: 'reportId',
      },
      {
        title: '当前状态',
        dataIndex: 'currentStatus',
        key: 'currentStatus',
      },
    ],
  },
};
