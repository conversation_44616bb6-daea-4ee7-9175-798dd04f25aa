<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { completeOrder } from '../RepairOrders.api';

  // Emits
  const emit = defineEmits(['success', 'register']);

  const { createMessage } = useMessage();
  const isUpdate = ref(true);
  const rowId = ref('');

  // 表单配置
  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    labelWidth: 100,
    baseColProps: { span: 24 },
    schemas: [
      {
        field: 'orderId',
        label: '工单编号',
        component: 'Input',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'faultTitle',
        label: '故障标题',
        component: 'Input',
        componentProps: {
          disabled: true,
        },
      },
      {
        field: 'handleImages',
        label: '处理图片',
        component: 'JImageUpload',
        componentProps: {
          fileMax: 5,
          text: '点击上传处理图片',
        },
      },
      {
        field: 'handleAttachment',
        label: '处理附件',
        component: 'JUpload',
        componentProps: {
          fileMax: 3,
          text: '点击上传处理附件',
        },
      },
      {
        field: 'remark',
        label: '完成备注',
        component: 'InputTextArea',
        required: true,
        componentProps: {
          placeholder: '请输入处理完成的详细说明...',
          rows: 4,
        },
      },
    ],
    showActionButtonGroup: false,
    showResetButton: false,
    actionColOptions: {
      span: 23,
    },
  });

  // Modal标题
  const title = computed(() => '处理完成');

  // 表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    // 重置表单
    await resetFields();
    setModalProps({ confirmLoading: false });
    isUpdate.value = !!data?.isUpdate;

    if (unref(isUpdate)) {
      rowId.value = data.record.id;
      // 设置表单数据
      await setFieldsValue({
        orderId: data.record.id,
        faultTitle: data.record.faultTitle,
        handleImages: '',
        handleAttachment: '',
        remark: '',
      });
    }
  });

  // 提交处理完成
  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });
      
      const result = await completeOrder({
        id: rowId.value,
        handleImages: values.handleImages,
        handleAttachment: values.handleAttachment,
        remark: values.remark
      });
      
      if (result) {
        createMessage.success('工单处理完成！');
        closeModal();
        emit('success');
      }
    } catch (error) {
      console.error('处理完成失败:', error);
      if (error.response && error.response.data && error.response.data.message) {
        createMessage.error(error.response.data.message);
      } else {
        createMessage.error('工单处理完成失败！');
      }
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>
