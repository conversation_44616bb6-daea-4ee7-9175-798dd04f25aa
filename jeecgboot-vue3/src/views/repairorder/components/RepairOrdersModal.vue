<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="1200" @ok="handleSubmit">
      <div class="repair-order-detail">
        <BasicForm @register="registerForm" name="RepairOrdersForm" />

        <!-- 处理详情 - 仅在详情模式且工单已完成时显示 -->
        <div v-if="isDetail && orderData.currentStatus === '5' && (orderData.handleImages || orderData.handleAttachment)" class="handle-detail-section">
          <a-divider orientation="left">
            <span class="section-title">处理详情</span>
          </a-divider>
          <div class="handle-detail-content">
            <a-row :gutter="16">
              <!-- 处理图片 -->
              <a-col :span="24" v-if="orderData.handleImages">
                <a-form-item label="处理图片" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                  <HistoryFileList :dataList="handleImageList" />
                </a-form-item>
              </a-col>

              <!-- 处理附件 -->
              <a-col :span="24" v-if="orderData.handleAttachment">
                <a-form-item label="处理附件" :label-col="{ span: 3 }" :wrapper-col="{ span: 21 }">
                  <HistoryFileList :dataList="handleAttachmentList" />
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>

        <!-- 操作日志表格 - 仅在详情模式下显示 -->
        <div v-if="isDetail && auditLogs.length > 0" class="audit-logs-section">
          <a-divider orientation="left">
            <span class="section-title">操作日志</span>
          </a-divider>
          <BasicTable
            :columns="auditLogColumns"
            :dataSource="auditLogs"
            :pagination="false"
            :showIndexColumn="false"
            size="small"
            :scroll="{ x: 800 }"
          />
        </div>
      </div>
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref, reactive} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import {BasicTable} from '/@/components/Table';
    import {formSchema, auditLogColumns, getDetailFormSchema} from '../RepairOrders.data';
    import {saveOrUpdate, queryById} from '../RepairOrders.api';
    import { useMessage } from '/@/hooks/web/useMessage';
    import { getDateByPicker } from '/@/utils';
    import HistoryFileList from '/@/components/jeecg/comment/HistoryFileList.vue';
    const { createMessage } = useMessage();
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const isDetail = ref(false);
    const auditLogs = ref([]);
    const orderData = ref({});
    const handleImageList = ref([]);
    const handleAttachmentList = ref([]);
    //表单配置
    const [registerForm, { setProps,resetFields, setFieldsValue, validate, scrollToField, updateSchema }] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: 24}
    });
    //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await resetFields();
        auditLogs.value = [];
        setModalProps({confirmLoading: false,showCancelBtn:!!data?.showFooter,showOkBtn:!!data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        isDetail.value = !data?.showFooter; // 详情模式是showFooter为false的时候

        if (unref(isUpdate)) {
            // 如果是详情模式（showFooter为false），获取包含操作日志的详细数据
            if (!data?.showFooter) {
                try {
                    const result = await queryById({id: data.record.id});
                    if (result && result.repairOrder) {
                        // 设置操作日志数据和工单数据
                        auditLogs.value = result.auditLogs || [];
                        orderData.value = result.repairOrder;
                        
                        // 根据工单状态动态设置表单字段
                        const detailFormSchema = getDetailFormSchema(result.repairOrder);
                        await updateSchema(detailFormSchema);
                        
                        await setFieldsValue({
                            ...result.repairOrder,
                        });
                        
                        // 处理文件列表数据
                        const { imageList, attachmentList } = convertToSeparateFileLists(result.repairOrder);
                        handleImageList.value = imageList;
                        handleAttachmentList.value = attachmentList;
                    }
                } catch (error) {
                    console.error('获取工单详情失败:', error);
                    // 如果获取详情失败，使用原始数据
                    await setFieldsValue({
                        ...data.record,
                    });
                    orderData.value = data.record;
                    const { imageList, attachmentList } = convertToSeparateFileLists(data.record);
                    handleImageList.value = imageList;
                    handleAttachmentList.value = attachmentList;
                }
            } else {
                // 编辑模式，使用原始数据
                await setFieldsValue({
                    ...data.record,
                });
                orderData.value = data.record;
                const { imageList, attachmentList } = convertToSeparateFileLists(data.record);
                handleImageList.value = imageList;
                handleAttachmentList.value = attachmentList;
            }
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    //日期个性化选择
    const fieldPickers = reactive({
    });
    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));
    //表单提交事件
    async function handleSubmit(v) {
        try {
            let values = await validate();
            // 预处理日期数据
            changeDateValue(values);
            setModalProps({confirmLoading: true});
            //提交表单
            await saveOrUpdate(values, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } catch ({ errorFields }) {
           if (errorFields) {
             const firstField = errorFields[0];
             if (firstField) {
               scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
             }
           }
           return Promise.reject(errorFields);
        } finally {
            setModalProps({confirmLoading: false});
        }
    }

    /**
     * 处理日期值
     * @param formData 表单数据
     */
    const changeDateValue = (formData) => {
        if (formData && fieldPickers) {
            for (let key in fieldPickers) {
                if (formData[key]) {
                    formData[key] = getDateByPicker(formData[key], fieldPickers[key]);
                }
            }
        }
    }

    // 将工单数据转换为分离的图片和附件列表
    function convertToSeparateFileLists(orderData) {
        const imageList = [];
        const attachmentList = [];

        // 处理图片
        if (orderData.handleImages) {
            const images = orderData.handleImages.split(',').filter(img => img.trim());
            images.forEach(imagePath => {
                if (imagePath) {
                    imageList.push({
                        name: getFileName(imagePath),
                        path: imagePath,
                        url: imagePath,
                        type: 'image/jpeg', // 假设为图片类型
                        size: 0 // 大小未知
                    });
                }
            });
        }

        // 处理附件
        if (orderData.handleAttachment) {
            const attachments = orderData.handleAttachment.split(',').filter(file => file.trim());
            attachments.forEach(filePath => {
                if (filePath) {
                    attachmentList.push({
                        name: getFileName(filePath),
                        path: filePath,
                        url: filePath,
                        type: 'application/octet-stream', // 通用文件类型
                        size: 0 // 大小未知
                    });
                }
            });
        }

        return { imageList, attachmentList };
    }

    // 获取文件名
    function getFileName(filePath) {
        if (!filePath) return '';
        const parts = filePath.split('/');
        return parts[parts.length - 1];
    };

</script>

<style lang="less" scoped>
  .repair-order-detail {
    .handle-detail-section {
      margin-top: 24px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1890ff;
      }
    }

    .audit-logs-section {
      margin-top: 24px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #1890ff;
      }
    }
  }

  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }

  :deep(.ant-divider-horizontal.ant-divider-with-text-left) {
    margin: 16px 0;
  }
</style>
