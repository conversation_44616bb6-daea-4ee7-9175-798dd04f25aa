<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" :width="600" @ok="handleSubmit">
    <div class="audit-form">
      <a-form ref="formRef" :model="formData" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <a-form-item label="工单编号">
          <a-input v-model:value="formData.orderId" disabled />
        </a-form-item>

        <a-form-item label="故障标题">
          <a-input v-model:value="formData.faultTitle" disabled />
        </a-form-item>

        <a-form-item label="当前状态">
          <a-tag :color="getStatusColor(formData.currentStatus)">
            {{ getStatusText(formData.currentStatus) }}
          </a-tag>
        </a-form-item>

        <a-form-item label="审核结果" name="auditStatus" :rules="[{ required: true, message: '请选择审核结果!' }]">
          <a-radio-group v-model:value="formData.auditStatus" @change="handleAuditStatusChange">
            <a-radio value="approve">
              <span style="color: #52c41a; font-weight: bold;">通过</span>
            </a-radio>
            <a-radio value="reject">
              <span style="color: #ff4d4f; font-weight: bold;">驳回</span>
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 当审核通过时，必须指定负责人 -->
        <a-form-item
          v-if="formData.auditStatus === 'approve'"
          label="指定负责人"
          name="principalId"
          :rules="[{ required: true, message: '审核通过时必须指定负责人!' }]"
        >
          <j-select-user
            v-model:value="formData.principalId"
            placeholder="请选择负责人"
            :multiple="false"
          />
        </a-form-item>

        <a-form-item
          label="审核意见"
          name="remark"
          :rules="[{ required: true, message: '请输入审核意见!' }]"
        >
          <a-textarea
            v-model:value="formData.remark"
            :rows="4"
            :placeholder="getPlaceholderText()"
          />
        </a-form-item>
      </a-form>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { auditOrder } from '../RepairOrders.api';
  import { Tag as ATag } from 'ant-design-vue';
  import JSelectUser from '/@/components/Form/src/jeecg/components/JSelectUser.vue';

  const { createMessage } = useMessage();

  // Emits声明
  const emit = defineEmits(['register', 'success']);

  const formRef = ref();
  const formData = reactive({
    orderId: '',
    faultTitle: '',
    currentStatus: '',
    auditStatus: 'approve',
    principalId: '',
    remark: ''
  });

  const title = computed(() => {
    const statusText = formData.currentStatus === '3' ? '重新提交工单' : '工单';
    return formData.auditStatus === 'approve' ? `${statusText}审核通过` : `${statusText}审核驳回`;
  });

  // 表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    // 重置表单
    formRef.value?.resetFields();
    setModalProps({ confirmLoading: false });

    // 设置表单数据
    formData.orderId = data.record.id;
    formData.faultTitle = data.record.faultTitle;
    formData.currentStatus = data.record.currentStatus;
    formData.auditStatus = data.auditStatus || 'approve';
    // 修复：如果审核状态为通过，且记录中已有负责人，则使用该负责人
    formData.principalId = (data.auditStatus === 'approve' || formData.auditStatus === 'approve') && data.record.principalId ? data.record.principalId : '';
    formData.remark = '';
  });

  // 审核状态改变时的处理
  function handleAuditStatusChange() {
    // 如果改为驳回，清空负责人选择
    if (formData.auditStatus === 'reject') {
      formData.principalId = '';
    }
    // 重新验证表单
    formRef.value?.clearValidate();
  }

  // 提交审核
  async function handleSubmit() {
    try {
      await formRef.value?.validate();
      setModalProps({ confirmLoading: true });

      const result = await auditOrder({
        id: formData.orderId,
        auditStatus: formData.auditStatus,
        principalId: formData.auditStatus === 'approve' ? formData.principalId : undefined,
        remark: formData.remark
      });

      if (result) {
        const message = formData.auditStatus === 'approve' ? '审核通过成功，已指定负责人！' : '工单驳回成功！';
        createMessage.success(message);
        closeModal();
        emit('success');
      }
    } catch (error) {
      console.error('审核操作失败:', error);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  // 获取状态颜色
  function getStatusColor(status: string) {
    const colorMap = {
      '1': 'blue',
      '2': 'red',
      '3': 'orange',
      '4': 'green',
      '5': 'green',
      '6': 'default'
    };
    return colorMap[status] || 'default';
  }

  // 获取状态文本
  function getStatusText(status: string) {
    const textMap = {
      '1': '审核中',
      '2': '已驳回',
      '3': '重新提交',
      '4': '处理中',
      '5': '已完成',
      '6': '已关闭'
    };
    return textMap[status] || '未知状态';
  }

  // 获取占位符文本
  function getPlaceholderText() {
    return formData.auditStatus === 'approve'
      ? '请输入审核通过的意见和说明...'
      : '请输入驳回原因...';
  }
</script>

<style scoped>
.audit-form {
  padding: 16px;
}
</style>
